import React, { useEffect, useRef } from 'react';
import { Animated, StyleSheet, View, ActivityIndicator } from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';

interface LoadingProps {
  visible: boolean;
  message?: string;
  overlay?: boolean;
  size?: 'small' | 'large';
}

export function Loading({ 
  visible, 
  message = 'Yükleniyor...', 
  overlay = true, 
  size = 'large' 
}: LoadingProps) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;

  useEffect(() => {
    if (visible) {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.spring(scaleAnim, {
          toValue: 1,
          tension: 100,
          friction: 8,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 0.8,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [visible]);

  if (!visible) return null;

  const content = (
    <Animated.View
      style={[
        styles.container,
        {
          backgroundColor: overlay ? colors.surface : 'transparent',
          opacity: fadeAnim,
          transform: [{ scale: scaleAnim }],
        },
      ]}
    >
      <View style={[styles.content, { backgroundColor: colors.background }]}>
        <ActivityIndicator 
          size={size} 
          color={colors.primary} 
          style={styles.spinner}
        />
        {message && (
          <ThemedText style={[styles.message, { color: colors.text }]}>
            {message}
          </ThemedText>
        )}
      </View>
    </Animated.View>
  );

  if (overlay) {
    return (
      <View style={styles.overlay}>
        {content}
      </View>
    );
  }

  return content;
}

// Inline Loading Component (for buttons, etc.)
export function InlineLoading({ 
  size = 'small', 
  color 
}: { 
  size?: 'small' | 'large'; 
  color?: string; 
}) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  
  return (
    <ActivityIndicator 
      size={size} 
      color={color || colors.primary} 
      style={styles.inlineSpinner}
    />
  );
}

// Skeleton Loading Component
export function SkeletonLoading({ 
  width = '100%', 
  height = 20, 
  borderRadius = 4 
}: { 
  width?: string | number; 
  height?: number; 
  borderRadius?: number; 
}) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const animatedValue = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const animation = Animated.loop(
      Animated.sequence([
        Animated.timing(animatedValue, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(animatedValue, {
          toValue: 0,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    );
    animation.start();

    return () => animation.stop();
  }, []);

  const opacity = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [0.3, 0.7],
  });

  return (
    <Animated.View
      style={[
        styles.skeleton,
        {
          width,
          height,
          borderRadius,
          backgroundColor: colors.border,
          opacity,
        },
      ]}
    />
  );
}

// Loading Button Component
export function LoadingButton({ 
  onPress, 
  loading = false, 
  disabled = false, 
  title, 
  style, 
  textStyle 
}: {
  onPress: () => void;
  loading?: boolean;
  disabled?: boolean;
  title: string;
  style?: any;
  textStyle?: any;
}) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];

  return (
    <Animated.View
      style={[
        styles.loadingButton,
        {
          backgroundColor: disabled || loading ? colors.border : colors.primary,
        },
        style,
      ]}
    >
      <Animated.View style={styles.buttonContent}>
        {loading ? (
          <InlineLoading color="white" />
        ) : (
          <ThemedText
            style={[
              styles.buttonText,
              {
                color: disabled ? colors.icon : 'white',
              },
              textStyle,
            ]}
            onPress={disabled || loading ? undefined : onPress}
          >
            {title}
          </ThemedText>
        )}
      </Animated.View>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  container: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  content: {
    padding: 30,
    borderRadius: 12,
    alignItems: 'center',
    minWidth: 120,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  spinner: {
    marginBottom: 15,
  },
  message: {
    fontSize: 16,
    textAlign: 'center',
  },
  inlineSpinner: {
    marginHorizontal: 5,
  },
  skeleton: {
    marginVertical: 4,
  },
  loadingButton: {
    paddingVertical: 15,
    paddingHorizontal: 30,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 50,
  },
  buttonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
  },
});
