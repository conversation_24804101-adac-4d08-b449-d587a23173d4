/**
 * Farketmez App - Modern color palette
 * Designed for a vibrant and engaging user experience
 */

const tintColorLight = '#6366F1'; // Modern indigo
const tintColorDark = '#A855F7'; // Modern purple

export const Colors = {
  light: {
    text: '#1F2937', // Dark gray for better readability
    background: '#FFFFFF',
    tint: tintColorLight,
    icon: '#6B7280', // Medium gray
    tabIconDefault: '#9CA3AF', // Light gray
    tabIconSelected: tintColorLight,
    primary: '#6366F1', // Indigo
    secondary: '#EC4899', // Pink
    accent: '#10B981', // Emerald
    warning: '#F59E0B', // Amber
    error: '#EF4444', // Red
    surface: '#F9FAFB', // Very light gray
    border: '#E5E7EB', // Light border
  },
  dark: {
    text: '#F9FAFB', // Very light gray
    background: '#111827', // Dark blue-gray
    tint: tintColorDark,
    icon: '#9CA3AF', // Medium gray
    tabIconDefault: '#6B7280', // Darker gray
    tabIconSelected: tintColorDark,
    primary: '#A855F7', // Purple
    secondary: '#F472B6', // Light pink
    accent: '#34D399', // Light emerald
    warning: '#FBBF24', // Light amber
    error: '#F87171', // Light red
    surface: '#1F2937', // Dark surface
    border: '#374151', // Dark border
  },
};
