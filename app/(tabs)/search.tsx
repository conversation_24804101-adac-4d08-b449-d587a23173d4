import { StyleSheet, ScrollView, View, TextInput, Pressable, FlatList } from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { SkeletonLoading } from '@/components/ui/Loading';
import { useState, useEffect } from 'react';

// Mock data for search results
const mockData = [
  { id: '1', title: 'React Native Geliştirme', category: 'Teknoloji', description: 'Modern mobil uygulama geliştirme' },
  { id: '2', title: 'UI/UX Tasarım', category: 'Tasarım', description: 'Kullanıcı deneyimi tasarımı' },
  { id: '3', title: 'JavaScript ES6+', category: 'Programlama', description: 'Modern JavaScript özellikleri' },
  { id: '4', title: 'TypeScript', category: 'Programlama', description: 'Tip güvenli JavaScript' },
  { id: '5', title: 'Expo Framework', category: 'Teknoloji', description: 'React Native geliştirme platformu' },
  { id: '6', title: 'Figma Tasarım', category: 'Tasarım', description: 'Profesyonel tasarım araçları' },
];

const recentSearches = ['React Native', 'UI Design', 'TypeScript', 'Expo'];
const popularSearches = ['JavaScript', 'Mobile App', 'Web Development', 'Design System'];

export default function SearchScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [showResults, setShowResults] = useState(false);

  useEffect(() => {
    if (searchQuery.length > 0) {
      setLoading(true);
      setShowResults(true);
      
      // Simulate API call delay
      const timer = setTimeout(() => {
        const filtered = mockData.filter(item =>
          item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
          item.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
          item.category.toLowerCase().includes(searchQuery.toLowerCase())
        );
        setSearchResults(filtered);
        setLoading(false);
      }, 500);

      return () => clearTimeout(timer);
    } else {
      setShowResults(false);
      setSearchResults([]);
      setLoading(false);
    }
  }, [searchQuery]);

  const handleSearchPress = (query: string) => {
    setSearchQuery(query);
  };

  const clearSearch = () => {
    setSearchQuery('');
    setShowResults(false);
  };

  const renderSearchResult = ({ item }: { item: any }) => (
    <Pressable 
      style={[styles.resultItem, { backgroundColor: colors.surface, borderColor: colors.border }]}
      onPress={() => console.log('Selected:', item.title)}
    >
      <View style={[styles.resultIcon, { backgroundColor: colors.primary + '20' }]}>
        <IconSymbol name="magnifyingglass" size={20} color={colors.primary} />
      </View>
      <View style={styles.resultContent}>
        <ThemedText style={styles.resultTitle}>{item.title}</ThemedText>
        <ThemedText style={[styles.resultCategory, { color: colors.primary }]}>
          {item.category}
        </ThemedText>
        <ThemedText style={[styles.resultDescription, { opacity: 0.7 }]}>
          {item.description}
        </ThemedText>
      </View>
      <IconSymbol name="chevron.right" size={16} color={colors.icon} />
    </Pressable>
  );

  const renderSkeletonItem = () => (
    <View style={[styles.resultItem, { backgroundColor: colors.surface, borderColor: colors.border }]}>
      <SkeletonLoading width={40} height={40} borderRadius={20} />
      <View style={styles.resultContent}>
        <SkeletonLoading width="70%" height={16} />
        <SkeletonLoading width="40%" height={14} />
        <SkeletonLoading width="90%" height={12} />
      </View>
    </View>
  );

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: colors.surface }]}>
        <ThemedText type="title" style={styles.headerTitle}>
          Arama 🔍
        </ThemedText>
      </View>

      {/* Search Input */}
      <View style={styles.searchContainer}>
        <View style={[styles.searchInputContainer, { backgroundColor: colors.surface, borderColor: colors.border }]}>
          <IconSymbol name="magnifyingglass" size={20} color={colors.icon} style={styles.searchIcon} />
          <TextInput
            style={[styles.searchInput, { color: colors.text }]}
            placeholder="Ne aramak istiyorsunuz?"
            placeholderTextColor={colors.icon}
            value={searchQuery}
            onChangeText={setSearchQuery}
            autoCapitalize="none"
            autoCorrect={false}
          />
          {searchQuery.length > 0 && (
            <Pressable onPress={clearSearch} style={styles.clearButton}>
              <IconSymbol name="xmark.circle" size={20} color={colors.icon} />
            </Pressable>
          )}
        </View>
      </View>

      {/* Content */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {showResults ? (
          <View>
            <ThemedText style={[styles.sectionTitle, { color: colors.primary }]}>
              Arama Sonuçları ({searchResults.length})
            </ThemedText>
            
            {loading ? (
              <View>
                {[1, 2, 3].map((_, index) => (
                  <View key={index}>{renderSkeletonItem()}</View>
                ))}
              </View>
            ) : (
              <FlatList
                data={searchResults}
                renderItem={renderSearchResult}
                keyExtractor={(item) => item.id}
                scrollEnabled={false}
                ListEmptyComponent={
                  <View style={styles.emptyContainer}>
                    <IconSymbol name="magnifyingglass" size={48} color={colors.icon} />
                    <ThemedText style={[styles.emptyText, { color: colors.icon }]}>
                      Arama sonucu bulunamadı
                    </ThemedText>
                    <ThemedText style={[styles.emptySubtext, { color: colors.icon }]}>
                      Farklı anahtar kelimeler deneyin
                    </ThemedText>
                  </View>
                }
              />
            )}
          </View>
        ) : (
          <View>
            {/* Recent Searches */}
            <View style={styles.section}>
              <ThemedText style={[styles.sectionTitle, { color: colors.primary }]}>
                Son Aramalar
              </ThemedText>
              <View style={styles.tagsContainer}>
                {recentSearches.map((search, index) => (
                  <Pressable
                    key={index}
                    style={[styles.tag, { backgroundColor: colors.surface, borderColor: colors.border }]}
                    onPress={() => handleSearchPress(search)}
                  >
                    <IconSymbol name="arrow.clockwise" size={14} color={colors.icon} />
                    <ThemedText style={styles.tagText}>{search}</ThemedText>
                  </Pressable>
                ))}
              </View>
            </View>

            {/* Popular Searches */}
            <View style={styles.section}>
              <ThemedText style={[styles.sectionTitle, { color: colors.secondary }]}>
                Popüler Aramalar
              </ThemedText>
              <View style={styles.tagsContainer}>
                {popularSearches.map((search, index) => (
                  <Pressable
                    key={index}
                    style={[styles.tag, { backgroundColor: colors.primary + '20', borderColor: colors.primary }]}
                    onPress={() => handleSearchPress(search)}
                  >
                    <IconSymbol name="star.fill" size={14} color={colors.primary} />
                    <ThemedText style={[styles.tagText, { color: colors.primary }]}>{search}</ThemedText>
                  </Pressable>
                ))}
              </View>
            </View>
          </View>
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingTop: 60,
    paddingBottom: 20,
    paddingHorizontal: 20,
    alignItems: 'center',
  },
  headerTitle: {
    marginBottom: 8,
  },
  searchContainer: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 25,
    borderWidth: 1,
    paddingHorizontal: 15,
    height: 50,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    height: '100%',
  },
  clearButton: {
    padding: 5,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
  },
  section: {
    marginBottom: 30,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 10,
  },
  tag: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    gap: 6,
  },
  tagText: {
    fontSize: 14,
  },
  resultItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    borderRadius: 12,
    marginBottom: 10,
    borderWidth: 1,
  },
  resultIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 15,
  },
  resultContent: {
    flex: 1,
  },
  resultTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  resultCategory: {
    fontSize: 12,
    fontWeight: '500',
    marginBottom: 4,
  },
  resultDescription: {
    fontSize: 14,
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 15,
    marginBottom: 5,
  },
  emptySubtext: {
    fontSize: 14,
  },
});
