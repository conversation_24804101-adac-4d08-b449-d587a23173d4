import { StyleSheet, ScrollView, Pressable, Dimensions, View } from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';

const { width } = Dimensions.get('window');

export default function HomeScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];

  return (
    <ScrollView style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Hero Section */}
      <View style={[styles.heroSection, { backgroundColor: colors.primary }]}>
        <ThemedView style={[styles.heroContent, { backgroundColor: 'transparent' }]}>
          <ThemedText style={[styles.heroTitle, { color: 'white' }]}>
            Farketmez App&apos;e Hoş Geldiniz! 🎉
          </ThemedText>
          <ThemedText style={[styles.heroSubtitle, { color: 'rgba(255,255,255,0.9)' }]}>
            Modern, hızlı ve kullanıcı dostu deneyim
          </ThemedText>
        </ThemedView>
      </View>

      {/* Feature Cards */}
      <ThemedView style={styles.featuresContainer}>
        <ThemedText type="title" style={styles.sectionTitle}>
          Özellikler
        </ThemedText>

        <ThemedView style={styles.cardsContainer}>
          <FeatureCard
            title="🚀 Hızlı"
            description="Optimize edilmiş performans"
            color={colors.accent}
          />
          <FeatureCard
            title="🎨 Modern"
            description="Çağdaş tasarım dili"
            color={colors.primary}
          />
          <FeatureCard
            title="📱 Responsive"
            description="Her cihazda mükemmel"
            color={colors.secondary}
          />
          <FeatureCard
            title="🔒 Güvenli"
            description="Verileriniz güvende"
            color={colors.warning}
          />
        </ThemedView>
      </ThemedView>

      {/* Quick Actions */}
      <ThemedView style={styles.actionsContainer}>
        <ThemedText type="subtitle" style={styles.sectionTitle}>
          Hızlı İşlemler
        </ThemedText>

        <Pressable
          style={[styles.actionButton, { backgroundColor: colors.primary }]}
          onPress={() => console.log('Başla pressed')}
        >
          <ThemedText style={[styles.actionButtonText, { color: 'white' }]}>
            Hemen Başla
          </ThemedText>
        </Pressable>

        <Pressable
          style={[styles.actionButton, styles.secondaryButton, { borderColor: colors.primary }]}
          onPress={() => console.log('Daha fazla pressed')}
        >
          <ThemedText style={[styles.actionButtonText, { color: colors.primary }]}>
            Daha Fazla Bilgi
          </ThemedText>
        </Pressable>
      </ThemedView>
    </ScrollView>
  );
}

// Feature Card Component
function FeatureCard({ title, description, color }: { title: string; description: string; color: string }) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];

  return (
    <ThemedView style={[styles.featureCard, { backgroundColor: colors.surface, borderColor: colors.border }]}>
      <ThemedText style={[styles.featureTitle, { color }]}>
        {title}
      </ThemedText>
      <ThemedText style={styles.featureDescription}>
        {description}
      </ThemedText>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  heroSection: {
    paddingTop: 60,
    paddingBottom: 40,
    paddingHorizontal: 20,
    minHeight: 200,
  },
  heroContent: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  heroTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 10,
  },
  heroSubtitle: {
    fontSize: 16,
    textAlign: 'center',
    opacity: 0.9,
  },
  featuresContainer: {
    padding: 20,
  },
  sectionTitle: {
    marginBottom: 20,
    textAlign: 'center',
  },
  cardsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: 15,
  },
  featureCard: {
    width: (width - 60) / 2,
    padding: 20,
    borderRadius: 12,
    borderWidth: 1,
    alignItems: 'center',
    minHeight: 120,
    justifyContent: 'center',
  },
  featureTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
    textAlign: 'center',
  },
  featureDescription: {
    fontSize: 14,
    textAlign: 'center',
    opacity: 0.7,
  },
  actionsContainer: {
    padding: 20,
    paddingBottom: 40,
  },
  actionButton: {
    paddingVertical: 15,
    paddingHorizontal: 30,
    borderRadius: 25,
    alignItems: 'center',
    marginBottom: 15,
  },
  secondaryButton: {
    backgroundColor: 'transparent',
    borderWidth: 2,
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
});
