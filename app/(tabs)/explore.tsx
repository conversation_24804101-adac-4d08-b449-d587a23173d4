import { StyleSheet, ScrollView, View } from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Collapsible } from '@/components/Collapsible';

export default function ExploreScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];

  return (
    <ScrollView style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: colors.surface }]}>
        <ThemedText type="title" style={styles.headerTitle}>
          Keşfet 🔍
        </ThemedText>
        <ThemedText style={styles.headerSubtitle}>
          Farketmez App&apos;in özelliklerini keşfedin
        </ThemedText>
      </View>

      {/* Content */}
      <ThemedView style={styles.content}>
        <Collapsible title="🚀 Uygulama Özellikleri">
          <ThemedText>
            Farketmez App modern React Native teknolojileri ile geliştirilmiştir.
          </ThemedText>
          <ThemedText>
            • Expo Router ile dosya tabanlı navigasyon
          </ThemedText>
          <ThemedText>
            • TypeScript desteği
          </ThemedText>
          <ThemedText>
            • Responsive tasarım
          </ThemedText>
        </Collapsible>

        <Collapsible title="📱 Platform Desteği">
          <ThemedText>
            Bu uygulama Android, iOS ve web platformlarında çalışır.
          </ThemedText>
          <ThemedText>
            Web versiyonunu açmak için terminalde <ThemedText type="defaultSemiBold">w</ThemedText> tuşuna basın.
          </ThemedText>
        </Collapsible>

        <Collapsible title="🎨 Tasarım Sistemi">
          <ThemedText>
            Modern renk paleti ve tipografi kullanılmıştır.
          </ThemedText>
          <ThemedText>
            • Açık ve koyu tema desteği
          </ThemedText>
          <ThemedText>
            • Tutarlı bileşen tasarımı
          </ThemedText>
          <ThemedText>
            • Erişilebilirlik odaklı yaklaşım
          </ThemedText>
        </Collapsible>

        <Collapsible title="⚡ Performans">
          <ThemedText>
            Optimize edilmiş performans için:
          </ThemedText>
          <ThemedText>
            • React Native Reanimated animasyonları
          </ThemedText>
          <ThemedText>
            • Lazy loading ve code splitting
          </ThemedText>
          <ThemedText>
            • Efficient re-rendering
          </ThemedText>
        </Collapsible>

        <Collapsible title="🔧 Geliştirici Araçları">
          <ThemedText>
            Geliştirme deneyimini iyileştiren araçlar:
          </ThemedText>
          <ThemedText>
            • Hot reload ve fast refresh
          </ThemedText>
          <ThemedText>
            • ESLint ve Prettier entegrasyonu
          </ThemedText>
          <ThemedText>
            • Jest test framework&apos;ü
          </ThemedText>
        </Collapsible>
      </ThemedView>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingTop: 60,
    paddingBottom: 30,
    paddingHorizontal: 20,
    alignItems: 'center',
  },
  headerTitle: {
    marginBottom: 8,
  },
  headerSubtitle: {
    textAlign: 'center',
    opacity: 0.7,
  },
  content: {
    padding: 20,
  },
});
