import { StyleSheet, ScrollView, View, Switch, Alert } from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useState } from 'react';

export default function SettingsScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];

  // Settings state
  const [notifications, setNotifications] = useState(true);
  const [darkMode, setDarkMode] = useState(colorScheme === 'dark');
  const [autoSync, setAutoSync] = useState(true);
  const [analytics, setAnalytics] = useState(false);

  const handleNotificationChange = (value: boolean) => {
    setNotifications(value);
    Alert.alert('Bildirimler', value ? 'Bildirimler açıldı' : '<PERSON>ildirim<PERSON> kapatıldı');
  };

  const handleDarkModeChange = (value: boolean) => {
    setDarkMode(value);
    Alert.alert('Tema', 'Tema değişikliği için uygulamayı yeniden başlatın');
  };

  return (
    <ScrollView style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: colors.surface }]}>
        <ThemedText type="title" style={styles.headerTitle}>
          Ayarlar ⚙️
        </ThemedText>
        <ThemedText style={styles.headerSubtitle}>
          Uygulama tercihlerinizi yönetin
        </ThemedText>
      </View>

      {/* General Settings */}
      <ThemedView style={styles.section}>
        <ThemedText style={[styles.sectionTitle, { color: colors.primary }]}>
          Genel
        </ThemedText>
        
        <SettingItem
          icon="bell.fill"
          title="Bildirimler"
          subtitle="Push bildirimleri al"
          value={notifications}
          onValueChange={handleNotificationChange}
          colors={colors}
        />
        
        <SettingItem
          icon="gear"
          title="Koyu Tema"
          subtitle="Karanlık modu etkinleştir"
          value={darkMode}
          onValueChange={handleDarkModeChange}
          colors={colors}
        />
        
        <SettingItem
          icon="arrow.clockwise"
          title="Otomatik Senkronizasyon"
          subtitle="Verileri otomatik güncelle"
          value={autoSync}
          onValueChange={setAutoSync}
          colors={colors}
        />
      </ThemedView>

      {/* Privacy Settings */}
      <ThemedView style={styles.section}>
        <ThemedText style={[styles.sectionTitle, { color: colors.secondary }]}>
          Gizlilik
        </ThemedText>
        
        <SettingItem
          icon="chart.bar"
          title="Analitik Verileri"
          subtitle="Kullanım verilerini paylaş"
          value={analytics}
          onValueChange={setAnalytics}
          colors={colors}
        />
      </ThemedView>

      {/* App Info */}
      <ThemedView style={styles.section}>
        <ThemedText style={[styles.sectionTitle, { color: colors.accent }]}>
          Uygulama Bilgileri
        </ThemedText>
        
        <InfoItem
          icon="info.circle"
          title="Versiyon"
          value="1.0.0"
          colors={colors}
        />
        
        <InfoItem
          icon="star.fill"
          title="Değerlendirme"
          value="App Store'da değerlendir"
          colors={colors}
          onPress={() => Alert.alert('Değerlendirme', 'App Store\'a yönlendirilecek')}
        />
        
        <InfoItem
          icon="heart.fill"
          title="Destek"
          value="Bize ulaşın"
          colors={colors}
          onPress={() => Alert.alert('Destek', '<EMAIL>')}
        />
      </ThemedView>

      {/* Danger Zone */}
      <ThemedView style={styles.section}>
        <ThemedText style={[styles.sectionTitle, { color: colors.error }]}>
          Tehlikeli Bölge
        </ThemedText>
        
        <InfoItem
          icon="exclamationmark.triangle"
          title="Verileri Sıfırla"
          value="Tüm verileri temizle"
          colors={colors}
          isDanger={true}
          onPress={() => Alert.alert(
            'Verileri Sıfırla',
            'Bu işlem geri alınamaz. Devam etmek istiyor musunuz?',
            [
              { text: 'İptal', style: 'cancel' },
              { text: 'Sıfırla', style: 'destructive' }
            ]
          )}
        />
      </ThemedView>
    </ScrollView>
  );
}

// Setting Item with Switch
function SettingItem({ 
  icon, 
  title, 
  subtitle, 
  value, 
  onValueChange, 
  colors 
}: { 
  icon: any; 
  title: string; 
  subtitle: string; 
  value: boolean; 
  onValueChange: (value: boolean) => void; 
  colors: any; 
}) {
  return (
    <View style={[styles.settingItem, { backgroundColor: colors.surface, borderColor: colors.border }]}>
      <View style={[styles.iconContainer, { backgroundColor: colors.primary + '20' }]}>
        <IconSymbol name={icon} size={20} color={colors.primary} />
      </View>
      <View style={styles.textContainer}>
        <ThemedText style={styles.itemTitle}>{title}</ThemedText>
        <ThemedText style={[styles.itemSubtitle, { opacity: 0.7 }]}>{subtitle}</ThemedText>
      </View>
      <Switch
        value={value}
        onValueChange={onValueChange}
        trackColor={{ false: colors.border, true: colors.primary + '40' }}
        thumbColor={value ? colors.primary : colors.icon}
      />
    </View>
  );
}

// Info Item (read-only)
function InfoItem({ 
  icon, 
  title, 
  value, 
  colors, 
  isDanger = false, 
  onPress 
}: { 
  icon: any; 
  title: string; 
  value: string; 
  colors: any; 
  isDanger?: boolean; 
  onPress?: () => void; 
}) {
  const textColor = isDanger ? colors.error : colors.text;
  const iconColor = isDanger ? colors.error : colors.primary;
  
  return (
    <View style={[styles.settingItem, { backgroundColor: colors.surface, borderColor: colors.border }]}>
      <View style={[styles.iconContainer, { backgroundColor: iconColor + '20' }]}>
        <IconSymbol name={icon} size={20} color={iconColor} />
      </View>
      <View style={styles.textContainer}>
        <ThemedText style={[styles.itemTitle, { color: textColor }]}>{title}</ThemedText>
        <ThemedText 
          style={[styles.itemSubtitle, { opacity: 0.7, color: textColor }]}
          onPress={onPress}
        >
          {value}
        </ThemedText>
      </View>
      {onPress && (
        <IconSymbol name="chevron.right" size={16} color={colors.icon} />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingTop: 60,
    paddingBottom: 30,
    paddingHorizontal: 20,
    alignItems: 'center',
  },
  headerTitle: {
    marginBottom: 8,
  },
  headerSubtitle: {
    textAlign: 'center',
    opacity: 0.7,
  },
  section: {
    margin: 20,
    marginBottom: 10,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    borderRadius: 12,
    marginBottom: 8,
    borderWidth: 1,
  },
  iconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  textContainer: {
    flex: 1,
  },
  itemTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  itemSubtitle: {
    fontSize: 14,
  },
});
