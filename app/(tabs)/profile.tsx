import { StyleSheet, ScrollView, View, Pressable, Alert } from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { IconSymbol } from '@/components/ui/IconSymbol';

export default function ProfileScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];

  const handleEditProfile = () => {
    Alert.alert('Profil Düzenle', 'Profil düzenleme özelliği yakında gelecek!');
  };

  const handleSettings = () => {
    Alert.alert('Ayarlar', 'Ayarlar sayfası yakında gelecek!');
  };

  const handleLogout = () => {
    Alert.alert(
      '<PERSON>ıkış Yap',
      'He<PERSON>b<PERSON>nızdan çıkmak istediğinizden emin misiniz?',
      [
        { text: 'İptal', style: 'cancel' },
        { text: 'Çık<PERSON>ş Yap', style: 'destructive', onPress: () => console.log('Logout') }
      ]
    );
  };

  return (
    <ScrollView style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: colors.primary }]}>
        <View style={styles.profileImageContainer}>
          <View style={[styles.profileImage, { backgroundColor: colors.surface }]}>
            <IconSymbol name="person.fill" size={40} color={colors.primary} />
          </View>
        </View>
        <ThemedText style={[styles.userName, { color: 'white' }]}>
          Kullanıcı Adı
        </ThemedText>
        <ThemedText style={[styles.userEmail, { color: 'rgba(255,255,255,0.8)' }]}>
          <EMAIL>
        </ThemedText>
      </View>

      {/* Profile Stats */}
      <ThemedView style={styles.statsContainer}>
        <View style={styles.statItem}>
          <ThemedText style={[styles.statNumber, { color: colors.primary }]}>
            42
          </ThemedText>
          <ThemedText style={styles.statLabel}>
            Aktivite
          </ThemedText>
        </View>
        <View style={styles.statItem}>
          <ThemedText style={[styles.statNumber, { color: colors.secondary }]}>
            128
          </ThemedText>
          <ThemedText style={styles.statLabel}>
            Puan
          </ThemedText>
        </View>
        <View style={styles.statItem}>
          <ThemedText style={[styles.statNumber, { color: colors.accent }]}>
            15
          </ThemedText>
          <ThemedText style={styles.statLabel}>
            Başarı
          </ThemedText>
        </View>
      </ThemedView>

      {/* Menu Items */}
      <ThemedView style={styles.menuContainer}>
        <MenuItemComponent
          icon="gear"
          title="Ayarlar"
          subtitle="Uygulama ayarlarını düzenle"
          onPress={handleSettings}
          colors={colors}
        />
        <MenuItemComponent
          icon="bell.fill"
          title="Bildirimler"
          subtitle="Bildirim tercihlerini yönet"
          onPress={() => Alert.alert('Bildirimler', 'Bildirim ayarları yakında!')}
          colors={colors}
        />
        <MenuItemComponent
          icon="heart.fill"
          title="Favoriler"
          subtitle="Beğendiğin içerikleri gör"
          onPress={() => Alert.alert('Favoriler', 'Favoriler sayfası yakında!')}
          colors={colors}
        />
        <MenuItemComponent
          icon="info.circle"
          title="Hakkında"
          subtitle="Uygulama hakkında bilgi"
          onPress={() => Alert.alert('Hakkında', 'Farketmez App v1.0.0')}
          colors={colors}
        />
      </ThemedView>

      {/* Action Buttons */}
      <ThemedView style={styles.actionsContainer}>
        <Pressable
          style={[styles.editButton, { backgroundColor: colors.primary }]}
          onPress={handleEditProfile}
        >
          <ThemedText style={[styles.buttonText, { color: 'white' }]}>
            Profili Düzenle
          </ThemedText>
        </Pressable>

        <Pressable
          style={[styles.logoutButton, { borderColor: colors.error }]}
          onPress={handleLogout}
        >
          <ThemedText style={[styles.buttonText, { color: colors.error }]}>
            Çıkış Yap
          </ThemedText>
        </Pressable>
      </ThemedView>
    </ScrollView>
  );
}

// Menu Item Component
function MenuItemComponent({ 
  icon, 
  title, 
  subtitle, 
  onPress, 
  colors 
}: { 
  icon: any; 
  title: string; 
  subtitle: string; 
  onPress: () => void; 
  colors: any; 
}) {
  return (
    <Pressable 
      style={[styles.menuItem, { backgroundColor: colors.surface, borderColor: colors.border }]}
      onPress={onPress}
    >
      <View style={[styles.menuIconContainer, { backgroundColor: colors.primary + '20' }]}>
        <IconSymbol name={icon} size={24} color={colors.primary} />
      </View>
      <View style={styles.menuTextContainer}>
        <ThemedText style={styles.menuTitle}>{title}</ThemedText>
        <ThemedText style={[styles.menuSubtitle, { opacity: 0.7 }]}>{subtitle}</ThemedText>
      </View>
      <IconSymbol name="chevron.right" size={20} color={colors.icon} />
    </Pressable>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingTop: 60,
    paddingBottom: 30,
    paddingHorizontal: 20,
    alignItems: 'center',
  },
  profileImageContainer: {
    marginBottom: 15,
  },
  profileImage: {
    width: 80,
    height: 80,
    borderRadius: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  userName: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  userEmail: {
    fontSize: 16,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: 20,
    marginHorizontal: 20,
    borderRadius: 12,
    marginTop: -15,
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  statLabel: {
    fontSize: 14,
    opacity: 0.7,
  },
  menuContainer: {
    padding: 20,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    borderRadius: 12,
    marginBottom: 10,
    borderWidth: 1,
  },
  menuIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 15,
  },
  menuTextContainer: {
    flex: 1,
  },
  menuTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  menuSubtitle: {
    fontSize: 14,
  },
  actionsContainer: {
    padding: 20,
    paddingBottom: 40,
  },
  editButton: {
    paddingVertical: 15,
    paddingHorizontal: 30,
    borderRadius: 25,
    alignItems: 'center',
    marginBottom: 15,
  },
  logoutButton: {
    paddingVertical: 15,
    paddingHorizontal: 30,
    borderRadius: 25,
    alignItems: 'center',
    borderWidth: 2,
    backgroundColor: 'transparent',
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
  },
});
